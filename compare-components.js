#!/usr/bin/env node

/**
 * Component Comparison Script
 * Compares PodcastNameGeneratorFramer.tsx with src/PodcastNameGenerator.tsx
 * and identifies key differences for synchronization
 */

import fs from 'fs';
import path from 'path';

const FRAMER_COMPONENT = 'PodcastNameGeneratorFramer.tsx';
const LOCALHOST_COMPONENT = 'src/PodcastNameGenerator.tsx';
const LOCALHOST_CSS = 'src/PodcastNameGenerator.css';

class ComponentComparison {
  constructor() {
    this.differences = [];
    this.recommendations = [];
  }

  async compareComponents() {
    console.log('🔍 Starting component comparison...\n');

    // Check if files exist
    if (!fs.existsSync(FRAMER_COMPONENT)) {
      console.error(`❌ ${FRAMER_COMPONENT} not found`);
      return;
    }
    if (!fs.existsSync(LOCALHOST_COMPONENT)) {
      console.error(`❌ ${LOCALHOST_COMPONENT} not found`);
      return;
    }

    const framerContent = fs.readFileSync(FRAMER_COMPONENT, 'utf8');
    const localhostContent = fs.readFileSync(LOCALHOST_COMPONENT, 'utf8');
    const cssContent = fs.existsSync(LOCALHOST_CSS) ? fs.readFileSync(LOCALHOST_CSS, 'utf8') : '';

    // Analyze key differences
    this.analyzeImports(framerContent, localhostContent);
    this.analyzeExports(framerContent, localhostContent);
    this.analyzeStyling(framerContent, localhostContent, cssContent);
    this.analyzeInterfaces(framerContent, localhostContent);
    this.analyzeFunctionality(framerContent, localhostContent);
    this.analyzeUserPreferences(framerContent, localhostContent);

    this.generateReport();
  }

  analyzeImports(framer, localhost) {
    console.log('📦 Analyzing imports...');
    
    const framerImports = this.extractImports(framer);
    const localhostImports = this.extractImports(localhost);

    if (localhostImports.includes('./PodcastNameGenerator.css') && !framerImports.includes('./PodcastNameGenerator.css')) {
      this.differences.push({
        type: 'STYLING',
        severity: 'HIGH',
        description: 'Framer component uses inline styles, localhost uses external CSS',
        framer: 'Inline styles in component',
        localhost: 'External CSS import'
      });
      
      this.recommendations.push({
        action: 'CONVERT_CSS_TO_INLINE',
        description: 'Convert external CSS to inline styles for Framer compatibility',
        priority: 'HIGH'
      });
    }
  }

  analyzeExports(framer, localhost) {
    console.log('📤 Analyzing exports...');
    
    const framerExport = framer.includes('export default function PodcastNameGenerator');
    const localhostExport = localhost.includes('const PodcastNameGenerator: React.FC');

    if (framerExport !== localhostExport) {
      this.differences.push({
        type: 'EXPORT_PATTERN',
        severity: 'MEDIUM',
        description: 'Different export patterns used',
        framer: framerExport ? 'Function declaration export' : 'Const declaration export',
        localhost: localhostExport ? 'Const declaration export' : 'Function declaration export'
      });
    }
  }

  analyzeStyling(framer, localhost, css) {
    console.log('🎨 Analyzing styling approach...');
    
    const framerHasInlineStyles = framer.includes('const styles = `');
    const localhostHasExternalCSS = css.length > 0;

    if (framerHasInlineStyles && localhostHasExternalCSS) {
      this.differences.push({
        type: 'STYLING_APPROACH',
        severity: 'HIGH',
        description: 'Different styling approaches - inline vs external',
        framer: 'Inline styles with template literals',
        localhost: 'External CSS file'
      });
    }
  }

  analyzeInterfaces(framer, localhost) {
    console.log('🔧 Analyzing interfaces...');
    
    const framerInterfaces = this.extractInterfaces(framer);
    const localhostInterfaces = this.extractInterfaces(localhost);

    // Check for interface differences
    const interfaceNames = [...new Set([...framerInterfaces, ...localhostInterfaces])];
    
    for (const interfaceName of interfaceNames) {
      if (!framerInterfaces.includes(interfaceName)) {
        this.differences.push({
          type: 'MISSING_INTERFACE',
          severity: 'MEDIUM',
          description: `Interface ${interfaceName} missing in Framer component`,
          framer: 'Missing',
          localhost: 'Present'
        });
      } else if (!localhostInterfaces.includes(interfaceName)) {
        this.differences.push({
          type: 'EXTRA_INTERFACE',
          severity: 'LOW',
          description: `Interface ${interfaceName} only in Framer component`,
          framer: 'Present',
          localhost: 'Missing'
        });
      }
    }
  }

  analyzeFunctionality(framer, localhost) {
    console.log('⚙️ Analyzing functionality...');
    
    // Check for key functions
    const keyFunctions = [
      'generatePodcastNames',
      'handleFeedback',
      'copyToClipboard',
      'checkDomainAvailability',
      'generateDomainName'
    ];

    for (const func of keyFunctions) {
      const framerHas = framer.includes(func);
      const localhostHas = localhost.includes(func);

      if (framerHas !== localhostHas) {
        this.differences.push({
          type: 'FUNCTION_MISMATCH',
          severity: 'HIGH',
          description: `Function ${func} availability differs`,
          framer: framerHas ? 'Present' : 'Missing',
          localhost: localhostHas ? 'Present' : 'Missing'
        });
      }
    }
  }

  analyzeUserPreferences(framer, localhost) {
    console.log('👤 Analyzing user preferences implementation...');
    
    // Check for green checkmarks (user wants them removed)
    const framerHasCheckmarks = framer.includes('benefit-checkmark') || framer.includes('✓');
    const localhostHasCheckmarks = localhost.includes('benefit-checkmark') || localhost.includes('✓');

    if (framerHasCheckmarks || localhostHasCheckmarks) {
      this.recommendations.push({
        action: 'REMOVE_GREEN_CHECKMARKS',
        description: 'User prefers to remove green checkmarks from domain displays',
        priority: 'HIGH'
      });
    }

    // Check domain suggestion limits
    const framerDomainLimit = this.extractDomainLimit(framer);
    const localhostDomainLimit = this.extractDomainLimit(localhost);

    if (framerDomainLimit !== 4 || localhostDomainLimit !== 4) {
      this.recommendations.push({
        action: 'LIMIT_DOMAIN_SUGGESTIONS',
        description: 'User prefers maximum of 4 domain suggestions',
        priority: 'HIGH'
      });
    }
  }

  extractImports(content) {
    const importRegex = /import.*from\s+['"]([^'"]+)['"]/g;
    const imports = [];
    let match;
    while ((match = importRegex.exec(content)) !== null) {
      imports.push(match[1]);
    }
    return imports;
  }

  extractInterfaces(content) {
    const interfaceRegex = /interface\s+(\w+)/g;
    const interfaces = [];
    let match;
    while ((match = interfaceRegex.exec(content)) !== null) {
      interfaces.push(match[1]);
    }
    return interfaces;
  }

  extractDomainLimit(content) {
    const limitMatch = content.match(/availableDomains\.slice\(0,\s*(\d+)\)/);
    return limitMatch ? parseInt(limitMatch[1]) : null;
  }

  generateReport() {
    console.log('\n📊 COMPARISON REPORT');
    console.log('='.repeat(50));

    if (this.differences.length === 0) {
      console.log('✅ No significant differences found!');
    } else {
      console.log(`\n🔍 Found ${this.differences.length} differences:\n`);
      
      this.differences.forEach((diff, index) => {
        console.log(`${index + 1}. ${diff.type} (${diff.severity})`);
        console.log(`   Description: ${diff.description}`);
        console.log(`   Framer: ${diff.framer}`);
        console.log(`   Localhost: ${diff.localhost}\n`);
      });
    }

    if (this.recommendations.length > 0) {
      console.log('\n💡 RECOMMENDATIONS:\n');
      
      this.recommendations.forEach((rec, index) => {
        console.log(`${index + 1}. ${rec.action} (${rec.priority})`);
        console.log(`   ${rec.description}\n`);
      });
    }

    // Generate action plan
    this.generateActionPlan();
  }

  generateActionPlan() {
    console.log('\n🎯 ACTION PLAN');
    console.log('='.repeat(50));

    const highPriorityActions = this.recommendations.filter(r => r.priority === 'HIGH');
    const mediumPriorityActions = this.recommendations.filter(r => r.priority === 'MEDIUM');
    const lowPriorityActions = this.recommendations.filter(r => r.priority === 'LOW');

    if (highPriorityActions.length > 0) {
      console.log('\n🔴 HIGH PRIORITY:');
      highPriorityActions.forEach((action, index) => {
        console.log(`   ${index + 1}. ${action.action}`);
      });
    }

    if (mediumPriorityActions.length > 0) {
      console.log('\n🟡 MEDIUM PRIORITY:');
      mediumPriorityActions.forEach((action, index) => {
        console.log(`   ${index + 1}. ${action.action}`);
      });
    }

    if (lowPriorityActions.length > 0) {
      console.log('\n🟢 LOW PRIORITY:');
      lowPriorityActions.forEach((action, index) => {
        console.log(`   ${index + 1}. ${action.action}`);
      });
    }

    console.log('\n✨ Ready to apply changes!');
  }
}

// Run the comparison
const comparison = new ComponentComparison();
comparison.compareComponents().catch(console.error);
