// Simple test to verify the component structure for Framer
const fs = require('fs');

const componentContent = fs.readFileSync('PodcastNameGeneratorFramer.tsx', 'utf8');

// Check for essential Framer requirements
const checks = {
  hasReactImport: /import\s+React/.test(componentContent),
  hasDefaultExport: /export\s+default\s+\w+/.test(componentContent),
  hasPropsInterface: /interface\s+\w+Props/.test(componentContent),
  hasComponentFunction: /function\s+\w+\s*\(/.test(componentContent),
  noDuplicateInterfaces: !(/interface\s+PodcastName[\s\S]*interface\s+PodcastName/.test(componentContent)),
  hasJSXReturn: /return\s*\([\s\S]*</.test(componentContent)
};

console.log('Framer Component Structure Check:');
console.log('================================');

Object.entries(checks).forEach(([check, passed]) => {
  console.log(`${passed ? '✅' : '❌'} ${check}: ${passed ? 'PASS' : 'FAIL'}`);
});

const allPassed = Object.values(checks).every(Boolean);
console.log('\n' + (allPassed ? '🎉 Component should work with Framer!' : '⚠️  Component may have issues with Framer'));

// Additional info
console.log('\nComponent Details:');
console.log(`- File size: ${(componentContent.length / 1024).toFixed(1)}KB`);
console.log(`- Lines: ${componentContent.split('\n').length}`);
console.log(`- Has TypeScript interfaces: ${/interface\s+\w+/.test(componentContent) ? 'Yes' : 'No'}`);
